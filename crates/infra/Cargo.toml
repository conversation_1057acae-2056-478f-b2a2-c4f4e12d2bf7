[package]
name = "infra"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
dbus-api = { path = "../dbus-api" }
network = { path = "../network" }

## Gestion d'erreurs
# Anyhow permet un certain sucre syntaxique autour des erreurs en Rust
anyhow = { version = "1.0.93" }
# thiserror, couplé à Anyhow, permet son sucre syntaxique mais en fluidifiant
# la création d'erreurs
thiserror = { version = "2.0.11" }

## Logs
# Tracing est la référence afin de tracer les appels du boitier
tracing = { version = "0.1.40" }
tracing-subscriber = { version = "0.3.18", features = ["env-filter"] }
tracing-appender = "0.2.3"
tokio = { version = "1", features = ["rt-multi-thread", "time"] }
dbus = { version = "0.9.7", features = ["futures"] }
at-commands = "0.5.5"

# La version est fixée à cause de notre version de Rust trop vieille pour la librairie
rusqlite = { version = "=0.31.0" }
dbus-tokio = "0.7.6"
surge-ping = "0.8.2"
rand = "0.9.0"
regex = "1.11.1"

[dev-dependencies]
tempfile = "3.19.1"
googletest = "0.14.0"
