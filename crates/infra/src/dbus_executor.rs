use std::{sync::Arc, time::Duration};

use dbus::nonblock::{self, SyncConnection};
use dbus_api::{
    board_manager::FrEnedisHALBoardManager1, led_manager::FrEnedisHALLEDManager1,
    modem_manager::FrEnedisHALModemManager1,
};
use dbus_tokio::connection::{self};
use network::{errors::NetworkError, led::LedState, operator::Operator, ports::HalPort};
use tracing::info;

/// Parce que nous utilisons la librairie C de DBUS, certains de nos appels restent bloquants.
/// Même en utilisant tokio, si une routine appelle une méthode qui bloque le thread... ça bloque tout.
/// La librarie dbus_tokio nous permet de rendre nos interactions avec DBUS asynchrones, à une exception
/// près : la connection initiale.

#[derive(Clone)]
pub struct DbusHalAdapter {
    connection: Arc<SyncConnection>,
}

impl DbusHalAdapter {
    pub fn new() -> Result<Self, NetworkError> {
        let (_resource, connection) = connection::new_system_sync()?;

        tokio::spawn(async {
            let err = _resource.await;
            panic!("Lost connection to D-Bus: {}", err);
        });

        Ok(Self { connection })
    }

    fn modem_proxy(&self) -> dbus::nonblock::Proxy<'_, Arc<SyncConnection>> {
        nonblock::Proxy::new(
            "fr.enedis.HAL.ModemManager1",
            "/fr/enedis/HAL/ModemManager1",
            Duration::from_secs(120),
            self.connection.clone(),
        )
    }

    fn board_proxy(&self) -> dbus::nonblock::Proxy<'_, Arc<SyncConnection>> {
        nonblock::Proxy::new(
            "fr.enedis.HAL.BoardManager1",
            "/fr/enedis/HAL/BoardManager1",
            Duration::from_secs(120),
            self.connection.clone(),
        )
    }
    fn led_proxy(&self) -> dbus::nonblock::Proxy<'_, Arc<SyncConnection>> {
        nonblock::Proxy::new(
            "fr.enedis.HAL.LEDManager1",
            "/fr/enedis/HAL/LEDManager1",
            Duration::from_secs(120),
            self.connection.clone(),
        )
    }
}

impl HalPort for DbusHalAdapter {
    async fn get_ads(&self) -> Result<String, NetworkError> {
        Ok(self.board_proxy().ads().await?)
    }

    async fn get_current_operator(&self) -> Result<Option<Operator>, NetworkError> {
        let proxy = self.modem_proxy();

        let (connected, operator_name) = proxy.current_operator().await?;
        if !connected {
            return Ok(None);
        }

        let operator = Operator::try_from(operator_name.as_ref())?;
        Ok(Some(operator))
    }

    async fn connect(&self, apn: &str, login: &str, password: &str) -> Result<(), NetworkError> {
        let proxy = self.modem_proxy();

        proxy.connect(apn, login, password).await?;

        Ok(())
    }

    async fn reboot_modem(&self) -> Result<(), NetworkError> {
        let proxy = self.modem_proxy();

        proxy.reboot_modem().await?;

        Ok(())
    }

    async fn reboot_bip(&self) -> Result<(), NetworkError> {
        info!("Reboot du BIP demandé.");
        Ok(())
    }

    async fn set_led_si_status(&self, state: LedState) -> Result<(), NetworkError> {
        let proxy = self.led_proxy();

        let dbus_led_state: u8 = match state {
            LedState::Off => 0,
            LedState::SlowBlink => 1,
            LedState::Blink => 2,
            LedState::FastBlink => 3,
            LedState::On => 4,
        };

        proxy.set_siled_status(dbus_led_state).await?;

        Ok(())
    }
}
