use std::{fs::read_to_string, net::Ipv4Addr, path::PathBuf, str::FromStr};

use anyhow::Context;
use network::{errors::NetworkError, ports::DnsResolverPort};
use regex::Regex;
use tracing::debug;

const RESOLV_CONF_REGEX: &str = r"nameserver ([\d\.]*)";
const RESOLV_CONF_FILENAME: &str = "resolv.conf";

#[derive(Clone)]
pub struct PppDnsResolverAdapter {
    pub path_to_resolv_conf_directory: PathBuf,
}

impl DnsResolverPort for PppDnsResolverAdapter {
    /// Récupère le DNS fourni par PPP.
    ///
    /// Recherche dans le fichier `/etc/ppp/resolv.conf` l'adresse du DNS primaire
    /// sur le filesystem
    fn get_dns_ip(&self) -> Result<Option<Ipv4Addr>, NetworkError> {
        let resolv_conf_path = self
            .path_to_resolv_conf_directory
            .join(RESOLV_CONF_FILENAME);

        if !resolv_conf_path.exists() {
            debug!(
                path = resolv_conf_path.to_str(),
                "fichier dns de ppp introuvable"
            );
            return Ok(None);
        }

        let resolv_conf_content = read_to_string(&resolv_conf_path).context(format!(
            "lecture du fichier dns à {}",
            resolv_conf_path.to_string_lossy()
        ))?;

        Ok(parse_dns_in_resolv_conf(&resolv_conf_content))
    }
}

fn parse_dns_in_resolv_conf(content: &str) -> Option<Ipv4Addr> {
    Regex::new(RESOLV_CONF_REGEX)
        .ok()
        .and_then(|re| re.captures(content))
        .and_then(|captures| captures.get(1))
        .and_then(|parsed_ip| Ipv4Addr::from_str(parsed_ip.as_str()).ok())
}

#[cfg(test)]
mod tests_resolv_conf_parser {
    use std::net::Ipv4Addr;

    use googletest::assert_that;
    use googletest::prelude::*;

    use super::parse_dns_in_resolv_conf;

    #[test]
    fn l_ip_est_recuperee_dans_le_contenu_du_resolv_conf() {
        let file_content = "nameserver ************";

        let parsed_dns = parse_dns_in_resolv_conf(file_content);

        assert_that!(parsed_dns, some(eq(Ipv4Addr::new(10, 139, 18, 99))))
    }

    #[test]
    fn la_premiere_ip_est_recuperee_quand_il_y_en_a_plusieurs() {
        let file_content = "nameserver ************\nnameserver ************";

        let parsed_dns = parse_dns_in_resolv_conf(file_content);

        assert_that!(parsed_dns, some(eq(Ipv4Addr::new(10, 139, 18, 99))))
    }
}

#[cfg(test)]
mod tests_resolver {
    use std::fs::File;
    use std::io::Write;
    use std::net::Ipv4Addr;
    use std::path::Path;

    use googletest::assert_that;
    use googletest::prelude::*;
    use network::ports::DnsResolverPort;

    use super::PppDnsResolverAdapter;
    use super::RESOLV_CONF_FILENAME;

    #[test]
    fn quand_le_fichier_resolv_conf_n_est_pas_present_aucun_dns_n_est_detecte() {
        let dir = tempfile::tempdir().unwrap();

        let dns_resolver = PppDnsResolverAdapter {
            path_to_resolv_conf_directory: dir.path().to_owned(),
        };

        let received_attached_dns = dns_resolver.get_dns_ip();

        assert_that!(received_attached_dns, ok(none()))
    }

    #[test]
    fn quand_le_fichier_resolv_conf_contient_une_ip_elle_est_renvoyee() {
        let attached_dns_ip: Ipv4Addr = "************".parse().unwrap();

        let resolv_conf_dir = tempfile::tempdir().unwrap();
        create_resolv_conf_with_dns(resolv_conf_dir.path(), &attached_dns_ip);

        let dns_resolver = PppDnsResolverAdapter {
            path_to_resolv_conf_directory: resolv_conf_dir.path().to_owned(),
        };

        let received_attached_dns = dns_resolver.get_dns_ip();

        assert_that!(received_attached_dns, ok(some(eq(&attached_dns_ip))))
    }

    /// Crée un fichier resolv.conf au chemin fourni
    fn create_resolv_conf_with_dns(path: &Path, ip: &Ipv4Addr) {
        let file_path = path.join(RESOLV_CONF_FILENAME);
        let mut file = File::create(file_path).unwrap();

        writeln!(&mut file, "nameserver {}", ip.to_string()).unwrap();
    }
}
