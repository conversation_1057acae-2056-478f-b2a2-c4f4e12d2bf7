use std::net::Ipv4Addr;

use network::{errors::NetworkError, ports::DnsResolverPort};

#[derive(Clone)]
pub struct DummyDnsResolverAdapter {
    pub dns_value: Ipv4Addr,
}

impl DnsResolverPort for DummyDnsResolverAdapter {
    /// Retourne l'IP précédemment fournie
    ///
    /// Pour des buts de test, cette méthode renvoie toujours la valeur
    /// qu'on lui a fourni.
    fn get_dns_ip(&self) -> Result<Option<Ipv4Addr>, NetworkError> {
        Ok(Some(self.dns_value.clone()))
    }
}
