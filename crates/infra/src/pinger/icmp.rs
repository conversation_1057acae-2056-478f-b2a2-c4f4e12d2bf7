use std::{
    net::{IpAddr, Ipv4Addr},
    time::Duration,
};

use network::ports::PingerPort;
use surge_ping::{Client, Config, PingIdentifier, PingSequence, ICMP};

#[derive(Clone)]
pub struct IcmpPingerAdapter {
    pub timeout: Duration,
}

impl PingerPort for IcmpPingerAdapter {
    async fn is_ip_reachable(&self, ip: Ipv4Addr) -> bool {
        let mut config_builder = Config::builder();

        config_builder = config_builder.interface("ppp0");
        config_builder = config_builder.kind(ICMP::V4);

        let config = config_builder.build();

        let Ok(client) = Client::new(&config) else {
            return false;
        };

        let payload = vec![0; 8];
        let mut pinger = client
            .pinger(IpAddr::from(ip.clone()), PingIdentifier(0))
            .await;

        pinger.timeout(self.timeout);

        pinger.ping(PingSequence(0), &payload).await.is_ok()
    }
}
