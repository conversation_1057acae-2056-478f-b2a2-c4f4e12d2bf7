use std::{future::Future, net::Ipv4Addr, ops::Range};

use crate::{
    configuration::NetworkConfiguration, datacenter::Datacenter, errors::NetworkError,
    led::LedState, operator::Operator,
};

pub trait HalPort: Clone + 'static {
    async fn get_current_operator(&self) -> Result<Option<Operator>, NetworkError>;
    async fn get_ads(&self) -> Result<String, NetworkError>;
    async fn connect(&self, apn: &str, login: &str, password: &str) -> Result<(), NetworkError>;
    async fn reboot_modem(&self) -> Result<(), NetworkError>;
    async fn reboot_bip(&self) -> Result<(), NetworkError>;
    async fn set_led_si_status(&self, state: LedState) -> Result<(), NetworkError>;
}

pub trait MsPort: Clone + 'static {
    fn get_radius(&self) -> Result<String, NetworkError>;
}

pub trait PingerPort: Clone + 'static {
    fn is_ip_reachable(&self, ip: Ipv4Addr) -> impl Future<Output = bool>;
}

pub trait RandomGeneratorPort: Clone + 'static {
    fn between(&self, range: Range<u64>) -> u64;
}

/// Abstraction vers l'entité en charge d'intéragir avec le DNS
pub trait DnsResolverPort: Clone + 'static {
    /// Récupère le DNS assigné
    ///
    /// Recherche sur le BIP le DNS primaire assigné, s'il est présent.
    /// Si aucun DNS n'est rattaché, la fonction renverra un `Ok(None)`.
    ///
    /// La méthode peut être potentiellement utilisée afin
    /// d'en déduire la configuration des datacenters.
    fn get_dns_ip(&self) -> Result<Option<Ipv4Addr>, NetworkError>;
}

pub trait ParamsPort: Clone + 'static {
    /// Récupère la configuration
    ///
    /// La méthode renvoie la configuration par défaut lors de n'importe quelle erreur
    fn fetch_configuration(&self) -> NetworkConfiguration;

    /// Renseigne le datacenter primaire en base de données
    fn set_primary_datacenter(&self, primary_datacenter: &Datacenter) -> Result<(), NetworkError>;
}
