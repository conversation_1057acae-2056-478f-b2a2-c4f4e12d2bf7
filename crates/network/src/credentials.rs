use std::time::Duration;

use tracing::info;

use crate::{
    configuration::NetworkConfiguration,
    operator::{self, Operator},
    ports::{HalPort, MsPort},
    retry::retry_async_until_success,
};

#[derive(<PERSON><PERSON>, Debug)]
pub struct Credentials {
    pub login: String,
    pub apn: String,
    pub password: String,
}

pub fn create_login(
    operator: &Operator,
    ads: &str,
    configuration: &NetworkConfiguration,
) -> String {
    match operator {
        Operator::Orange => {
            let mut login = String::new();
            login.push_str(ads);
            login.push_str("@");
            login.push_str(&configuration.orange_domain);

            login
        }
        _ => ads.to_owned(),
    }
}

//#[tracing::instrument(skip_all, fields(ads, operateur, apn))]
pub async fn fetch_credentials(
    hal: &impl HalPort,
    configuration: &NetworkConfiguration,
    ms: &impl MsPort,
) -> Credentials {
    info!("Récupération de l'ADS du boitier");
    let ads: String = retry_async_until_success(|| hal.get_ads(), Duration::from_secs(2)).await;
    info!("ADS obtenu");

    info!("Récupération de l'opérateur du modem");
    let operateur = operator::fetch_operator_until_obtained(hal).await;
    info!("Operateur obtenu");

    info!("Récupération de l'APN");
    let apn = configuration.get_apn_for(&operateur);
    info!("APN obtenu");

    info!("Récupération du mot de passe radius...");
    let Ok(radius) = ms.get_radius() else {
        todo!("gérer les soucis de module de sécurité")
    };
    info!("Radius obtenu");

    let login = create_login(&operateur, &ads, configuration);

    return Credentials {
        apn,
        login,
        password: radius,
    };
}
