use std::os::unix::net::UnixDatagram;
use std::{env, time::Duration};
use tracing::{debug, error, info, warn};

const NOTIFY_SOCKET_NAME: &str = "NOTIFY_SOCKET";
const READY_MESSAGE: &[u8] = b"READY=1";
const WATCHDOG_MESSAGE: &[u8] = b"WATCHDOG=1";

pub struct SystemdWatchdog {
    interval: Duration,
}

impl SystemdWatchdog {
    pub fn new(interval: Duration) -> Self {
        info!("Initializing systemd watchdog with default configuration");
        Self { interval }
    }

    pub async fn start(&self) {
        info!("Starting systemd watchdog service");

        // Send initial ready signal
        if let Err(e) = self.send_ready_signal() {
            warn!("Failed to send initial ready signal to systemd: {}", e);
        } else {
            info!("Successfully sent ready signal to systemd");
        }

        // Start heartbeat loop
        loop {
            debug!("Sending watchdog heartbeat");
            if let Err(e) = self.send_heartbeat() {
                warn!("Failed to send watchdog heartbeat: {}", e);
            } else {
                debug!("Watchdog heartbeat sent successfully");
            }

            tokio::time::sleep(self.interval).await;
        }
    }

    fn send_heartbeat(&self) -> Result<(), std::io::Error> {
        self.send_notification(WATCHDOG_MESSAGE)
    }

    fn send_ready_signal(&self) -> Result<(), std::io::Error> {
        self.send_notification(READY_MESSAGE)
    }

    fn send_notification(&self, message: &[u8]) -> Result<(), std::io::Error> {
        let socket_path = match env::var(NOTIFY_SOCKET_NAME) {
            Ok(path) => {
                debug!("Found systemd notification socket at: {}", path);
                path
            }
            Err(_) => {
                debug!(
                    "Systemd notification socket not available ({} not set)",
                    NOTIFY_SOCKET_NAME
                );
                return Ok(());
            }
        };

        let socket = UnixDatagram::unbound().map_err(|e| {
            error!("Failed to create Unix datagram socket: {}", e);
            e
        })?;

        socket.send_to(message, &socket_path).map_err(|e| {
            error!(
                "Failed to send notification to systemd socket '{}': {}",
                socket_path, e
            );
            e
        })?;

        debug!("Successfully sent notification to systemd");
        Ok(())
    }
}

#[cfg(test)]
mod tests;
