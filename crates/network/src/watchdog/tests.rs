use super::*;
use googletest::{assert_that, prelude::*};
use std::{env, time::Duration};
use tokio::time::{timeout, Instant};

// Helper function to set up environment variable
fn setup_notify_socket(path: &str) {
    env::set_var(NOTIFY_SOCKET_NAME, path);
}

// Helper function to clean up environment variable
fn cleanup_notify_socket() {
    env::remove_var(NOTIFY_SOCKET_NAME);
}

#[test]
fn test_new_creates_watchdog_with_correct_interval() {
    let interval = Duration::from_secs(5);
    let watchdog = SystemdWatchdog::new(interval);

    assert_that!(watchdog.interval, eq(interval));
}

#[test]
fn test_send_notification_without_notify_socket_returns_ok() {
    cleanup_notify_socket();

    let watchdog = SystemdWatchdog::new(Duration::from_secs(1));
    let result = watchdog.send_notification(WATCHDOG_MESSAGE);

    assert_that!(result, ok(anything()));
}

#[test]
fn test_send_notification_with_notify_socket_attempts_connection() {
    // Use a non-existent socket path to test the connection attempt
    setup_notify_socket("/tmp/non_existent_socket");

    let watchdog = SystemdWatchdog::new(Duration::from_secs(1));
    let result = watchdog.send_notification(WATCHDOG_MESSAGE);

    // Should fail because socket doesn't exist
    assert_that!(result, err(anything()));

    cleanup_notify_socket();
}

#[test]
fn test_send_heartbeat_calls_send_notification_with_watchdog_message() {
    cleanup_notify_socket();

    let watchdog = SystemdWatchdog::new(Duration::from_secs(1));
    let result = watchdog.send_heartbeat();

    assert_that!(result, ok(anything()));
}

#[test]
fn test_send_ready_signal_calls_send_notification_with_ready_message() {
    cleanup_notify_socket();

    let watchdog = SystemdWatchdog::new(Duration::from_secs(1));
    let result = watchdog.send_ready_signal();

    assert_that!(result, ok(anything()));
}

#[tokio::test]
async fn test_start_sends_ready_signal_initially() {
    cleanup_notify_socket();

    let watchdog = SystemdWatchdog::new(Duration::from_millis(100));

    // Start the watchdog and let it run for a short time
    let start_future = watchdog.start();
    let result = timeout(Duration::from_millis(50), start_future).await;

    // Should timeout because start() runs indefinitely
    assert_that!(result, err(anything()));
}

#[tokio::test]
async fn test_start_sends_periodic_heartbeats() {
    cleanup_notify_socket();

    let watchdog = SystemdWatchdog::new(Duration::from_millis(50));

    // Start the watchdog and let it run for enough time to send multiple heartbeats
    let start_future = watchdog.start();
    let result = timeout(Duration::from_millis(200), start_future).await;

    // Should timeout because start() runs indefinitely
    assert_that!(result, err(anything()));
}

#[tokio::test]
async fn test_start_continues_on_heartbeat_failure() {
    // Set up a socket path that will cause failures
    setup_notify_socket("/tmp/non_existent_socket_for_failure_test");

    let watchdog = SystemdWatchdog::new(Duration::from_millis(50));

    // Start the watchdog and let it run for enough time to attempt multiple heartbeats
    let start_future = watchdog.start();
    let result = timeout(Duration::from_millis(200), start_future).await;

    // Should timeout because start() continues running even with failures
    assert_that!(result, err(anything()));

    cleanup_notify_socket();
}

#[tokio::test]
async fn test_start_continues_on_ready_signal_failure() {
    // Set up a socket path that will cause failures
    setup_notify_socket("/tmp/non_existent_socket_for_ready_failure_test");

    let watchdog = SystemdWatchdog::new(Duration::from_millis(50));

    // Start the watchdog and let it run for a short time
    let start_future = watchdog.start();
    let result = timeout(Duration::from_millis(100), start_future).await;

    // Should timeout because start() continues running even if ready signal fails
    assert_that!(result, err(anything()));

    cleanup_notify_socket();
}

#[test]
fn test_send_notification_with_empty_socket_path() {
    setup_notify_socket("");

    let watchdog = SystemdWatchdog::new(Duration::from_secs(1));
    let result = watchdog.send_notification(WATCHDOG_MESSAGE);

    // Should fail because empty socket path is invalid
    assert_that!(result, err(anything()));

    cleanup_notify_socket();
}

#[tokio::test]
async fn test_watchdog_timing_precision() {
    cleanup_notify_socket();

    let interval = Duration::from_millis(100);
    let watchdog = SystemdWatchdog::new(interval);

    let start_time = Instant::now();
    let start_future = watchdog.start();

    // Let it run for approximately 2.5 intervals
    let result = timeout(Duration::from_millis(250), start_future).await;
    let elapsed = start_time.elapsed();

    // Should timeout and elapsed time should be close to 250ms
    assert_that!(result, err(anything()));
    assert_that!(elapsed.as_millis(), ge(240u128)); // Allow some tolerance
    assert_that!(elapsed.as_millis(), le(300u128)); // Allow some tolerance
}

#[cfg(unix)]
mod unix_socket_tests {
    use super::*;
    use std::os::unix::net::UnixDatagram;

    #[test]
    fn test_send_notification_with_real_unix_socket() {
        // Use a temporary socket path in /tmp
        let socket_path = "/tmp/test_watchdog_socket";

        // Remove any existing socket file
        let _ = std::fs::remove_file(socket_path);

        // Create a Unix socket to receive messages
        let receiver = match UnixDatagram::bind(socket_path) {
            Ok(socket) => socket,
            Err(_) => {
                // Skip test if we can't create socket (e.g., in restricted environment)
                return;
            }
        };

        setup_notify_socket(socket_path);

        let watchdog = SystemdWatchdog::new(Duration::from_secs(1));
        let result = watchdog.send_notification(WATCHDOG_MESSAGE);

        assert_that!(result, ok(anything()));

        // Try to receive the message
        let mut buffer = [0u8; 1024];
        let received = receiver.recv(&mut buffer);

        match received {
            Ok(size) => {
                assert_that!(&buffer[..size], eq(WATCHDOG_MESSAGE));
            }
            Err(_) => {
                // Socket might not receive immediately, but send should succeed
            }
        }

        cleanup_notify_socket();
        let _ = std::fs::remove_file(socket_path);
    }

    #[test]
    fn test_send_notification_with_permission_denied_socket() {
        // Try to use a path that would cause permission denied
        setup_notify_socket("/root/restricted_socket");

        let watchdog = SystemdWatchdog::new(Duration::from_secs(1));
        let result = watchdog.send_notification(WATCHDOG_MESSAGE);

        // Should fail due to permission issues
        assert_that!(result, err(anything()));

        cleanup_notify_socket();
    }

    #[test]
    fn test_send_notification_with_invalid_socket_path() {
        // Use a path with invalid characters or too long
        let invalid_path = "/".repeat(1000); // Very long path
        setup_notify_socket(&invalid_path);

        let watchdog = SystemdWatchdog::new(Duration::from_secs(1));
        let result = watchdog.send_notification(WATCHDOG_MESSAGE);

        // Should fail due to invalid path
        assert_that!(result, err(anything()));

        cleanup_notify_socket();
    }
}

#[tokio::test]
async fn test_start_with_zero_interval() {
    cleanup_notify_socket();

    let watchdog = SystemdWatchdog::new(Duration::ZERO);

    // Start the watchdog with zero interval
    let start_future = watchdog.start();
    let result = timeout(Duration::from_millis(100), start_future).await;

    // Should timeout because start() runs indefinitely even with zero interval
    assert_that!(result, err(anything()));
}

#[tokio::test]
async fn test_start_method_never_returns() {
    cleanup_notify_socket();

    let watchdog = SystemdWatchdog::new(Duration::from_millis(10));

    // Test that start() method runs indefinitely
    let start_time = Instant::now();
    let start_future = watchdog.start();
    let result = timeout(Duration::from_millis(100), start_future).await;
    let elapsed = start_time.elapsed();

    // Should timeout after approximately 100ms
    assert_that!(result, err(anything()));
    assert_that!(elapsed.as_millis(), ge(90u128));
    assert_that!(elapsed.as_millis(), le(150u128));
}

#[tokio::test]
async fn test_concurrent_watchdog_instances() {
    cleanup_notify_socket();

    let watchdog1 = SystemdWatchdog::new(Duration::from_millis(50));
    let watchdog2 = SystemdWatchdog::new(Duration::from_millis(75));

    // Start both watchdogs concurrently
    let future1 = watchdog1.start();
    let future2 = watchdog2.start();

    let result = timeout(Duration::from_millis(200), async {
        tokio::select! {
            _ = future1 => {},
            _ = future2 => {},
        }
    })
    .await;

    // Should timeout because both start() methods run indefinitely
    assert_that!(result, err(anything()));
}

#[test]
fn test_send_notification_error_path_coverage() {
    // Test the error path in send_notification when UnixDatagram::unbound() fails
    // This is hard to trigger in practice, but we can test the error handling logic

    // Set up a valid socket path
    setup_notify_socket("/tmp/test_socket_error");

    let watchdog = SystemdWatchdog::new(Duration::from_secs(1));

    // The actual error depends on system state, but we're testing that the function
    // properly handles and propagates errors from UnixDatagram::unbound()
    let result = watchdog.send_notification(WATCHDOG_MESSAGE);

    // Result could be Ok or Err depending on system state, but function should not panic
    match result {
        Ok(_) => {
            // Socket creation succeeded
        }
        Err(_) => {
            // Socket creation failed, which is also a valid test case
        }
    }

    cleanup_notify_socket();
}

#[test]
fn test_all_message_types_with_no_socket() {
    // Ensure all message types work when no socket is configured
    cleanup_notify_socket();

    let watchdog = SystemdWatchdog::new(Duration::from_secs(1));

    // Test READY_MESSAGE
    let result = watchdog.send_notification(READY_MESSAGE);
    assert_that!(result, ok(anything()));

    // Test WATCHDOG_MESSAGE
    let result = watchdog.send_notification(WATCHDOG_MESSAGE);
    assert_that!(result, ok(anything()));

    // Test custom message
    let result = watchdog.send_notification(b"STATUS=Running");
    assert_that!(result, ok(anything()));
}

#[tokio::test]
async fn test_start_error_handling_branches() {
    // Test both success and failure branches in start() method

    // First test with no socket (success branch for ready signal)
    cleanup_notify_socket();
    let watchdog = SystemdWatchdog::new(Duration::from_millis(10));

    let start_future = watchdog.start();
    let result = timeout(Duration::from_millis(50), start_future).await;
    assert_that!(result, err(anything())); // Should timeout

    // Then test with invalid socket (failure branch for ready signal)
    setup_notify_socket("/invalid/path/that/does/not/exist");
    let watchdog = SystemdWatchdog::new(Duration::from_millis(10));

    let start_future = watchdog.start();
    let result = timeout(Duration::from_millis(50), start_future).await;
    assert_that!(result, err(anything())); // Should timeout

    cleanup_notify_socket();
}
