use std::{future::Future, time::Duration};

use tracing::debug;

use crate::errors::NetworkError;

pub async fn retry_until_success<T>(
    function: impl Fn() -> Result<T, NetworkError>,
    delay: Duration,
) -> T {
    loop {
        match function() {
            Ok(obtained) => return obtained,
            Err(err) => {
                debug!(?err, "échec pendant le retry");
                tokio::time::sleep(delay).await;
            }
        }
    }
}

/// Nous sommes bloqués pour le moment en version 1.75.0 de Rust, où les `async closures` ne sont pas supportées.
/// J'ai dû passer par cette magie de génériques afin de pouvoir passer une fonction asynchrone en paramètre.
/// Source : https://stackoverflow.com/questions/60717746/how-to-accept-an-async-function-as-an-argument
/// L'issue ajoutant les async closures à Rust : https://github.com/rust-lang/rust/issues/62290
pub async fn retry_async_until_success<FUT, T>(function: impl Fn() -> FUT, delay: Duration) -> T
where
    FUT: Future<Output = Result<T, NetworkError>>,
{
    loop {
        match function().await {
            Ok(obtained) => return obtained,
            Err(err) => {
                debug!(?err, "échec pendant le retry");
                tokio::time::sleep(delay).await;
            }
        }
    }
}
