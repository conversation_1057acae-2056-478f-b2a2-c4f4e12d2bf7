use std::{net::Ipv4Addr, time::Duration};

use crate::{datacenter::Datacenter, operator::Operator};

#[derive(Clone, Debug)]
pub struct NetworkConfiguration {
    pub check_interval: Duration,
    pub ping_interval: Duration,
    pub ping_timeout: Duration,
    pub waiting_delay_after_modem_reset: Duration,
    pub maximum_random_ping_delay: Duration,
    pub ping_failures_limit_before_modem_reset: u8,
    pub modem_resets_before_bip_reboot: u8,
    pub orange_domain: String,
    pub orange_apn: String,
    pub bouygues_apn: String,
    pub sfr_apn: String,
    pub pacy_ip: Ipv4Addr,
    pub noe_ip: Ipv4Addr,
    pub datacenter_netmask: Ipv4Addr,
    pub primary_datacenter: Option<Datacenter>,
    pub watchdog_interval_secs: Duration,
}

impl NetworkConfiguration {
    pub fn get_apn_for(&self, operator: &Operator) -> String {
        match operator {
            Operator::Orange => &self.orange_apn,
            Operator::Bouygues => &self.bouygues_apn,
            Operator::Sfr => &self.sfr_apn,
        }
        .clone()
    }
}

pub fn default_configuration() -> NetworkConfiguration {
    NetworkConfiguration {
        check_interval: Duration::from_secs(60),
        ping_interval: Duration::from_secs(10),
        ping_timeout: Duration::from_secs(10),
        waiting_delay_after_modem_reset: Duration::from_secs(30),
        maximum_random_ping_delay: Duration::from_secs(5),
        ping_failures_limit_before_modem_reset: 3,
        modem_resets_before_bip_reboot: 3,
        orange_domain: "ccmaorat.fr.fg".to_owned(),
        orange_apn: "ccmaorat.fr".to_owned(),
        bouygues_apn: "ccmabytt".to_owned(),
        sfr_apn: "m2m03121".to_owned(),
        pacy_ip: [10, 139, 48, 3].into(),
        noe_ip: [10, 141, 48, 4].into(),
        datacenter_netmask: [255, 255, 255, 0].into(),
        primary_datacenter: None,
        watchdog_interval_secs: Duration::from_secs(45),
    }
}
