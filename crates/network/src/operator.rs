use std::time::Duration;

use crate::{errors::NetworkError, ports::HalPort};

#[derive(Debug, PartialEq, Eq, Clone)]
pub enum Operator {
    Orange,
    Bouygues,
    Sfr,
}

impl TryFrom<&str> for Operator {
    type Error = NetworkError;

    fn try_from(value: &str) -> Result<Self, Self::Error> {
        match value.to_uppercase().as_str() {
            "ORANGE" => Ok(Operator::Orange),
            "SFR" => Ok(Operator::Sfr),
            "BOUYGUES" => Ok(Operator::Bouygues),
            _ => Err(NetworkError::OperateurInconnu {
                operateur: value.to_owned(),
            }),
        }
    }
}

pub async fn fetch_operator_until_obtained(hal: &impl HalPort) -> Operator {
    loop {
        match hal.get_current_operator().await {
            Ok(Some(operator)) => return operator,
            _ => tokio::time::sleep(Duration::from_secs(1)).await,
        }
    }
}
