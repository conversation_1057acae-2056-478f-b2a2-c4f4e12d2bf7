use std::{net::Ipv4Addr, time::Duration};

use anyhow::anyhow;

use crate::{
    configuration::NetworkConfiguration,
    errors::NetworkError,
    ports::{DnsResolverPort, ParamsPort},
    retry::retry_until_success,
};

#[derive(<PERSON><PERSON>, Debug, PartialEq, Eq)]
pub struct DatacenterConfiguration {
    pub primary_datacenter_ip: Ipv4Addr,
    pub secondary_datacenter_ip: Ipv4Addr,
}

#[derive(Debug, PartialEq, Eq, Clone)]
pub enum Datacenter {
    NOE,
    PACY,
}

/// S'assure que le datacenter primaire est détecté
///
/// S'il n'a pas déjà été paramétré, recherche via le DNS le datacenter principal,
/// et met à jour la base de données en conséquence
pub async fn ensure_primary_datacenter_is_detected(
    dns_resolver: &impl DnsResolverPort,
    params: &impl ParamsPort,
    configuration: &NetworkConfiguration,
) -> Result<(), NetworkError> {
    if configuration.primary_datacenter.is_none() {
        let attached_datacenter_ip = retry_until_success(
            || {
                dns_resolver
                    .get_dns_ip()?
                    .ok_or(NetworkError::Other(anyhow!("aucun datacenter")))
            },
            Duration::from_secs(2),
        )
        .await;

        let attached_datacenter = attached_datacenter(configuration, &attached_datacenter_ip);
        params.set_primary_datacenter(&attached_datacenter)?;
    }

    Ok(())
}

/// Calcule la configuration des datacenter à partir de la conf en base de données
///
/// Si le datacenter primaire n'a pas été renseigné, la méthode ne renvoie pas de configuration
pub fn parse_datacenter_configuration(
    conf: &NetworkConfiguration,
) -> Option<DatacenterConfiguration> {
    match &conf.primary_datacenter {
        None => None,
        Some(primary_datacenter) => match primary_datacenter {
            Datacenter::NOE => Some(DatacenterConfiguration {
                primary_datacenter_ip: conf.noe_ip,
                secondary_datacenter_ip: conf.pacy_ip,
            }),
            Datacenter::PACY => Some(DatacenterConfiguration {
                primary_datacenter_ip: conf.pacy_ip,
                secondary_datacenter_ip: conf.noe_ip,
            }),
        },
    }
}

fn are_in_same_network(dns_ip: &Ipv4Addr, datacenter_ip: &Ipv4Addr, mask: &Ipv4Addr) -> bool {
    // Convertir les adresses et le masque en entiers 32 bits
    let dns_ip_bits = u32::from(dns_ip.clone());
    let datacenter_ip_bits = u32::from(datacenter_ip.clone());
    let mask_bits = u32::from(mask.clone());

    // Appliquer le masque aux adresses
    let dns_network = dns_ip_bits & mask_bits;
    let datacenter_network = datacenter_ip_bits & mask_bits;

    // Comparer les résultats
    dns_network == datacenter_network
}

fn attached_datacenter(
    configuration: &NetworkConfiguration,
    dns_ip_address: &Ipv4Addr,
) -> Datacenter {
    if are_in_same_network(
        dns_ip_address,
        &configuration.pacy_ip,
        &configuration.datacenter_netmask,
    ) {
        return Datacenter::PACY;
    } else {
        return Datacenter::NOE;
    }
}

#[cfg(test)]
mod tests {
    use super::attached_datacenter;
    use crate::{
        configuration::{default_configuration, NetworkConfiguration},
        datacenter::{ensure_primary_datacenter_is_detected, Datacenter},
        tests::{MockDnsResolverPort, MockParamsPort},
    };
    use googletest::{assert_that, prelude::eq};
    use mockall::predicate;
    use std::{net::Ipv4Addr, str::FromStr};

    fn default_test_configuration() -> NetworkConfiguration {
        let mut conf = default_configuration();
        conf.pacy_ip = "***********".parse().unwrap();
        conf.noe_ip = "***********".parse().unwrap();
        conf.datacenter_netmask = "*************".parse().unwrap();

        return conf;
    }

    #[test]
    fn l_ip_de_pacy_est_rattachee_lorsque_son_ip_correspond_a_celle_du_dns() {
        let conf = default_test_configuration();

        let ip_dns_rattache_a_pacy = Ipv4Addr::from_str("************").unwrap();
        let datacenter_rattache = attached_datacenter(&conf, &ip_dns_rattache_a_pacy);

        assert_that!(datacenter_rattache, eq(&Datacenter::PACY));
    }

    #[test]
    fn l_ip_de_noe_est_rattachee_lorsque_son_ip_correspond_a_celle_du_dns() {
        let conf = default_test_configuration();

        let ip_dns_rattache_a_noe = Ipv4Addr::from_str("*************").unwrap();
        let datacenter_rattache = attached_datacenter(&conf, &ip_dns_rattache_a_noe);

        assert_that!(datacenter_rattache, eq(&Datacenter::NOE));
    }

    #[tokio::test]
    async fn quand_la_configuration_est_en_base_de_donnees_cette_derniere_n_est_pas_mise_a_jour() {
        let mut conf = default_test_configuration();
        conf.primary_datacenter = Some(Datacenter::NOE);

        let mut mocked_dns = MockDnsResolverPort::new();
        mocked_dns.expect_get_dns_ip().never();

        let mut mocked_params = MockParamsPort::new();
        mocked_params.expect_set_primary_datacenter().never();
        mocked_params.expect_fetch_configuration().never();

        ensure_primary_datacenter_is_detected(&mocked_dns, &mocked_params, &conf)
            .await
            .expect("pas d'erreur");
    }

    #[tokio::test]
    async fn quand_le_datacenter_principal_n_est_pas_renseigne_on_demande_au_dns_le_datacenter_principal_et_on_le_sauvegarde(
    ) {
        let mut conf = default_test_configuration();
        conf.primary_datacenter = None;
        conf.noe_ip = "*************".parse().unwrap();
        conf.pacy_ip = "************".parse().unwrap();
        conf.datacenter_netmask = "*************".parse().unwrap();

        let ip_dns_rattache_a_pacy: Ipv4Addr = "************".parse().unwrap();

        let ip_detectee_par_dns = ip_dns_rattache_a_pacy.clone();
        let mut mocked_dns = MockDnsResolverPort::new();
        mocked_dns
            .expect_get_dns_ip()
            .returning(move || Ok(Some(ip_detectee_par_dns)));

        let mut mocked_params = MockParamsPort::new();
        mocked_params
            .expect_set_primary_datacenter()
            .with(predicate::eq(Datacenter::PACY))
            .once()
            .returning(|_| Ok(()));
        mocked_params.expect_fetch_configuration().never();

        ensure_primary_datacenter_is_detected(&mocked_dns, &mocked_params, &conf)
            .await
            .expect("pas d'erreur");
    }
}
