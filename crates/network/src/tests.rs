use std::{net::Ipv4Addr, ops::Range};

use mockall::mock;

use crate::{
    configuration::NetworkConfiguration, datacenter::Datacenter, errors::NetworkError,
    led::LedState, operator::Operator, ports,
};

mock! {
    pub HalPort {}
    impl ports::HalPort for HalPort {
        async fn get_current_operator(&self) -> Result<Option<Operator>, NetworkError>;
        async fn get_ads(&self) -> Result<String, NetworkError>;
        async fn connect(&self, apn: &str, login: &str, password: &str) -> Result<(), NetworkError>;
        async fn reboot_modem(&self) -> Result<(), NetworkError>;
        async fn reboot_bip(&self) -> Result<(), NetworkError>;
        async fn set_led_si_status(&self, state: LedState) -> Result<(), NetworkError>;
    }
    impl Clone for HalPort {   // specification of the trait to mock
        fn clone(&self) -> Self;
    }
}

mock! {
    pub PingerPort {}
    impl ports::PingerPort for PingerPort {
        async fn is_ip_reachable(&self, ip: Ipv4Addr) -> bool;
    }
    impl Clone for PingerPort {
        fn clone(&self) -> Self;
    }
}

mock! {
    pub RandomGeneratorPort {}
    impl ports::RandomGeneratorPort for RandomGeneratorPort {
        fn between(&self, range: Range<u64>) -> u64;
    }
    impl Clone for RandomGeneratorPort {
        fn clone(&self) -> Self;
    }
}

mock! {
    pub DnsResolverPort {}
    impl ports::DnsResolverPort for DnsResolverPort {
        fn get_dns_ip(&self) -> Result<Option<Ipv4Addr>, NetworkError>;
    }

    impl Clone for DnsResolverPort {
        fn clone(&self) -> Self;
    }
}

mock! {
    pub ParamsPort {}
    impl ports::ParamsPort for ParamsPort {
        fn fetch_configuration(&self) -> NetworkConfiguration;
        fn set_primary_datacenter(&self, primary_datacenter: &Datacenter) -> Result<(), NetworkError>;
    }

    impl Clone for ParamsPort {
        fn clone(&self) -> Self;
    }
}
