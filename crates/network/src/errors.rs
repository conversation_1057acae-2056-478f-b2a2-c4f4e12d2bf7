use thiserror::Error;
use tracing_appender::rolling::InitError;

/// Représente une erreur arrivée dans le modem manager
#[derive(Error, Debug)]
pub enum NetworkError {
    #[error("Erreur d'initialisation des logs")]
    EchecInitialisationLogs(#[from] InitError),

    #[error("Echec de communication avec le HAL")]
    HalInjoignable(#[from] dbus::Error),

    #[error("Le modem n'est pas prêt à recevoir les commandes")]
    ModemNonInitialise,

    #[error("La réponse de la commande AT `{commande:?}` est illisible : `{reponse:?}`")]
    ReponseCommandeAtIllisible { commande: String, reponse: String },

    #[error("L'opérateur \"{operateur:?}\" retourné par le modem est inconnu")]
    OperateurInconnu { operateur: String },

    #[error("Echec d'instanciation des paramètres: {chemin:?}")]
    EchecInitParams { chemin: String },

    #[error("Erreur technique")]
    Other(#[from] anyhow::Error),
}
