[package]
name = "network"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
## Gestion d'erreurs
# Anyhow permet un certain sucre syntaxique autour des erreurs en Rust
anyhow = { version = "1.0.93" }
# thiserror, couplé à Anyhow, permet son sucre syntaxique mais en fluidifiant
# la création d'erreurs
thiserror = { version = "2.0.11" }

## Logs
# Tracing est la référence afin de tracer les appels du boitier
tracing = { version = "0.1.40" }
tracing-subscriber = { version = "0.3.18", features = ["env-filter"] }
tracing-appender = "0.2.3"

dbus = { version = "0.9.7" }

tokio = { version = "^1.44", features = [
    "macros",
    "time",
    "rt-multi-thread",
    "test-util",
] }

[dev-dependencies]
googletest = "0.14.0"
mockall = "0.13.1"

[lints.rust]
async_fn_in_trait = "allow"
