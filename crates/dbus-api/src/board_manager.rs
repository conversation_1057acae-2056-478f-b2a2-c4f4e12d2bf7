// This code was autogenerated with `dbus-codegen-rust --file /tmp/dbus-api/src/main/dbus/specs/fr.enedis.HAL.BoardManager1.xml --output crates/dbus-api/src/board_manager.rs --client nonblock`, see https://github.com/diwic/dbus-rs
use dbus as dbus;
#[allow(unused_imports)]
use dbus::arg;
use dbus::nonblock;

pub trait FrEnedisHALBoardManager1 {
    fn ads(&self) -> nonblock::MethodReply<String>;
    fn cplc(&self) -> nonblock::MethodReply<String>;
    fn hwversion(&self) -> nonblock::MethodReply<(u8, u8, u8,)>;
    fn hwproduction_date_time(&self) -> nonblock::MethodReply<u64>;
}

#[derive(Debug)]
pub struct FrEnedisHALBoardManager1FatalEvent {
}

impl arg::AppendAll for FrEnedisHALBoardManager1FatalEvent {
    fn append(&self, _: &mut arg::IterAppend) {
    }
}

impl arg::ReadAll for FrEnedisHALBoardManager1FatalEvent {
    fn read(_: &mut arg::Iter) -> Result<Self, arg::TypeMismatchError> {
        Ok(FrEnedisHALBoardManager1FatalEvent {
        })
    }
}

impl dbus::message::SignalArgs for FrEnedisHALBoardManager1FatalEvent {
    const NAME: &'static str = "fatalEvent";
    const INTERFACE: &'static str = "fr.enedis.HAL.BoardManager1";
}

impl<'a, T: nonblock::NonblockReply, C: ::std::ops::Deref<Target=T>> FrEnedisHALBoardManager1 for nonblock::Proxy<'a, C> {

    fn ads(&self) -> nonblock::MethodReply<String> {
        <Self as nonblock::stdintf::org_freedesktop_dbus::Properties>::get(self, "fr.enedis.HAL.BoardManager1", "ADS")
    }

    fn cplc(&self) -> nonblock::MethodReply<String> {
        <Self as nonblock::stdintf::org_freedesktop_dbus::Properties>::get(self, "fr.enedis.HAL.BoardManager1", "CPLC")
    }

    fn hwversion(&self) -> nonblock::MethodReply<(u8, u8, u8,)> {
        <Self as nonblock::stdintf::org_freedesktop_dbus::Properties>::get(self, "fr.enedis.HAL.BoardManager1", "HWVersion")
    }

    fn hwproduction_date_time(&self) -> nonblock::MethodReply<u64> {
        <Self as nonblock::stdintf::org_freedesktop_dbus::Properties>::get(self, "fr.enedis.HAL.BoardManager1", "HWProductionDateTime")
    }
}
