// This code was autogenerated with `dbus-codegen-rust --file /tmp/dbus-api/src/main/dbus/specs/fr.enedis.HAL.DoorManager1.xml --output crates/dbus-api/src/door_manager.rs --client nonblock`, see https://github.com/diwic/dbus-rs
use dbus as dbus;
#[allow(unused_imports)]
use dbus::arg;
use dbus::nonblock;

pub trait FrEnedisHALDoorManager1 {
    fn get_door1_activity(&self) -> nonblock::MethodReply<Vec<(u64, bool,)>>;
    fn get_door2_activity(&self) -> nonblock::MethodReply<Vec<(u64, bool,)>>;
    fn clear_door1_activity(&self) -> nonblock::MethodReply<()>;
    fn clear_door2_activity(&self) -> nonblock::MethodReply<()>;
    fn door1_status(&self) -> nonblock::MethodReply<(u64, bool,)>;
    fn door2_status(&self) -> nonblock::MethodReply<(u64, bool,)>;
}

impl<'a, T: nonblock::NonblockReply, C: ::std::ops::Deref<Target=T>> FrEnedisHALDoorManager1 for nonblock::Proxy<'a, C> {

    fn get_door1_activity(&self) -> nonblock::MethodReply<Vec<(u64, bool,)>> {
        self.method_call("fr.enedis.HAL.DoorManager1", "GetDoor1Activity", ())
            .and_then(|r: (Vec<(u64, bool,)>, )| Ok(r.0, ))
    }

    fn get_door2_activity(&self) -> nonblock::MethodReply<Vec<(u64, bool,)>> {
        self.method_call("fr.enedis.HAL.DoorManager1", "GetDoor2Activity", ())
            .and_then(|r: (Vec<(u64, bool,)>, )| Ok(r.0, ))
    }

    fn clear_door1_activity(&self) -> nonblock::MethodReply<()> {
        self.method_call("fr.enedis.HAL.DoorManager1", "ClearDoor1Activity", ())
    }

    fn clear_door2_activity(&self) -> nonblock::MethodReply<()> {
        self.method_call("fr.enedis.HAL.DoorManager1", "ClearDoor2Activity", ())
    }

    fn door1_status(&self) -> nonblock::MethodReply<(u64, bool,)> {
        <Self as nonblock::stdintf::org_freedesktop_dbus::Properties>::get(self, "fr.enedis.HAL.DoorManager1", "Door1Status")
    }

    fn door2_status(&self) -> nonblock::MethodReply<(u64, bool,)> {
        <Self as nonblock::stdintf::org_freedesktop_dbus::Properties>::get(self, "fr.enedis.HAL.DoorManager1", "Door2Status")
    }
}
