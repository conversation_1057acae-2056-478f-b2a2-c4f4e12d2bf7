// This code was autogenerated with `dbus-codegen-rust --file /tmp/dbus-api/src/main/dbus/specs/fr.enedis.HAL.ModemManager1.xml --output crates/dbus-api/src/modem_manager.rs --client nonblock`, see https://github.com/diwic/dbus-rs
use dbus as dbus;
#[allow(unused_imports)]
use dbus::arg;
use dbus::nonblock;

pub trait FrEnedisHALModemManager1 {
    fn reboot_modem(&self) -> nonblock::MethodReply<()>;
    fn send_atcommand(&self, atcommand: &str) -> nonblock::MethodReply<String>;
    fn current_operator(&self) -> nonblock::MethodReply<(bool, String)>;
    fn connect(&self, apn: &str, login: &str, password: &str) -> nonblock::MethodReply<bool>;
    fn current_signal(&self) -> nonblock::MethodReply<(bool, i16)>;
    fn imei(&self) -> nonblock::MethodReply<String>;
    fn iccid(&self) -> nonblock::MethodReply<String>;
    fn modem_status(&self) -> nonblock::MethodReply<bool>;
    fn hibernate_mode(&self) -> nonblock::MethodReply<bool>;
    fn set_hibernate_mode(&self, value: bool) -> nonblock::MethodReply<()>;
    fn manufacturer(&self) -> nonblock::MethodReply<String>;
    fn model(&self) -> nonblock::MethodReply<String>;
    fn firmware_version(&self) -> nonblock::MethodReply<String>;
}

#[derive(Debug)]
pub struct FrEnedisHALModemManager1SimCardExtracted {
    pub is_card_inserted: bool,
}

impl arg::AppendAll for FrEnedisHALModemManager1SimCardExtracted {
    fn append(&self, i: &mut arg::IterAppend) {
        arg::RefArg::append(&self.is_card_inserted, i);
    }
}

impl arg::ReadAll for FrEnedisHALModemManager1SimCardExtracted {
    fn read(i: &mut arg::Iter) -> Result<Self, arg::TypeMismatchError> {
        Ok(FrEnedisHALModemManager1SimCardExtracted {
            is_card_inserted: i.read()?,
        })
    }
}

impl dbus::message::SignalArgs for FrEnedisHALModemManager1SimCardExtracted {
    const NAME: &'static str = "SimCardExtracted";
    const INTERFACE: &'static str = "fr.enedis.HAL.ModemManager1";
}

impl<'a, T: nonblock::NonblockReply, C: ::std::ops::Deref<Target=T>> FrEnedisHALModemManager1 for nonblock::Proxy<'a, C> {

    fn reboot_modem(&self) -> nonblock::MethodReply<()> {
        self.method_call("fr.enedis.HAL.ModemManager1", "RebootModem", ())
    }

    fn send_atcommand(&self, atcommand: &str) -> nonblock::MethodReply<String> {
        self.method_call("fr.enedis.HAL.ModemManager1", "SendATCommand", (atcommand, ))
            .and_then(|r: (String, )| Ok(r.0, ))
    }

    fn current_operator(&self) -> nonblock::MethodReply<(bool, String)> {
        self.method_call("fr.enedis.HAL.ModemManager1", "CurrentOperator", ())
    }

    fn connect(&self, apn: &str, login: &str, password: &str) -> nonblock::MethodReply<bool> {
        self.method_call("fr.enedis.HAL.ModemManager1", "Connect", (apn, login, password, ))
            .and_then(|r: (bool, )| Ok(r.0, ))
    }

    fn current_signal(&self) -> nonblock::MethodReply<(bool, i16)> {
        self.method_call("fr.enedis.HAL.ModemManager1", "CurrentSignal", ())
    }

    fn imei(&self) -> nonblock::MethodReply<String> {
        <Self as nonblock::stdintf::org_freedesktop_dbus::Properties>::get(self, "fr.enedis.HAL.ModemManager1", "IMEI")
    }

    fn iccid(&self) -> nonblock::MethodReply<String> {
        <Self as nonblock::stdintf::org_freedesktop_dbus::Properties>::get(self, "fr.enedis.HAL.ModemManager1", "ICCID")
    }

    fn modem_status(&self) -> nonblock::MethodReply<bool> {
        <Self as nonblock::stdintf::org_freedesktop_dbus::Properties>::get(self, "fr.enedis.HAL.ModemManager1", "ModemStatus")
    }

    fn hibernate_mode(&self) -> nonblock::MethodReply<bool> {
        <Self as nonblock::stdintf::org_freedesktop_dbus::Properties>::get(self, "fr.enedis.HAL.ModemManager1", "HibernateMode")
    }

    fn manufacturer(&self) -> nonblock::MethodReply<String> {
        <Self as nonblock::stdintf::org_freedesktop_dbus::Properties>::get(self, "fr.enedis.HAL.ModemManager1", "Manufacturer")
    }

    fn model(&self) -> nonblock::MethodReply<String> {
        <Self as nonblock::stdintf::org_freedesktop_dbus::Properties>::get(self, "fr.enedis.HAL.ModemManager1", "Model")
    }

    fn firmware_version(&self) -> nonblock::MethodReply<String> {
        <Self as nonblock::stdintf::org_freedesktop_dbus::Properties>::get(self, "fr.enedis.HAL.ModemManager1", "FirmwareVersion")
    }

    fn set_hibernate_mode(&self, value: bool) -> nonblock::MethodReply<()> {
        <Self as nonblock::stdintf::org_freedesktop_dbus::Properties>::set(self, "fr.enedis.HAL.ModemManager1", "HibernateMode", value)
    }
}
