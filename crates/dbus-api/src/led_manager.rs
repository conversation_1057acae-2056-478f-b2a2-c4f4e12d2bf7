// This code was autogenerated with `dbus-codegen-rust --file /tmp/dbus-api/src/main/dbus/specs/fr.enedis.HAL.LEDManager1.xml --output crates/dbus-api/src/led_manager.rs --client nonblock`, see https://github.com/diwic/dbus-rs
use dbus as dbus;
#[allow(unused_imports)]
use dbus::arg;
use dbus::nonblock;

pub trait FrEnedisHALLEDManager1 {
    fn siled_status(&self) -> nonblock::MethodReply<u8>;
    fn set_siled_status(&self, value: u8) -> nonblock::MethodReply<()>;
    fn power_led_status(&self) -> nonblock::MethodReply<u8>;
    fn set_power_led_status(&self, value: u8) -> nonblock::MethodReply<()>;
    fn eth1_led_status(&self) -> nonblock::MethodReply<u8>;
    fn set_eth1_led_status(&self, value: u8) -> nonblock::MethodReply<()>;
    fn eth2_led_status(&self) -> nonblock::MethodReply<u8>;
    fn set_eth2_led_status(&self, value: u8) -> nonblock::MethodReply<()>;
    fn cled_status(&self) -> nonblock::MethodReply<u8>;
    fn set_cled_status(&self, value: u8) -> nonblock::MethodReply<()>;
    fn rslled_status_and_color(&self) -> nonblock::MethodReply<u8>;
    fn set_rslled_status_and_color(&self, value: u8) -> nonblock::MethodReply<()>;
}

impl<'a, T: nonblock::NonblockReply, C: ::std::ops::Deref<Target=T>> FrEnedisHALLEDManager1 for nonblock::Proxy<'a, C> {

    fn siled_status(&self) -> nonblock::MethodReply<u8> {
        <Self as nonblock::stdintf::org_freedesktop_dbus::Properties>::get(self, "fr.enedis.HAL.LEDManager1", "SILedStatus")
    }

    fn power_led_status(&self) -> nonblock::MethodReply<u8> {
        <Self as nonblock::stdintf::org_freedesktop_dbus::Properties>::get(self, "fr.enedis.HAL.LEDManager1", "PowerLedStatus")
    }

    fn eth1_led_status(&self) -> nonblock::MethodReply<u8> {
        <Self as nonblock::stdintf::org_freedesktop_dbus::Properties>::get(self, "fr.enedis.HAL.LEDManager1", "Eth1LedStatus")
    }

    fn eth2_led_status(&self) -> nonblock::MethodReply<u8> {
        <Self as nonblock::stdintf::org_freedesktop_dbus::Properties>::get(self, "fr.enedis.HAL.LEDManager1", "Eth2LedStatus")
    }

    fn cled_status(&self) -> nonblock::MethodReply<u8> {
        <Self as nonblock::stdintf::org_freedesktop_dbus::Properties>::get(self, "fr.enedis.HAL.LEDManager1", "CLedStatus")
    }

    fn rslled_status_and_color(&self) -> nonblock::MethodReply<u8> {
        <Self as nonblock::stdintf::org_freedesktop_dbus::Properties>::get(self, "fr.enedis.HAL.LEDManager1", "RSLLedStatusAndColor")
    }

    fn set_siled_status(&self, value: u8) -> nonblock::MethodReply<()> {
        <Self as nonblock::stdintf::org_freedesktop_dbus::Properties>::set(self, "fr.enedis.HAL.LEDManager1", "SILedStatus", value)
    }

    fn set_power_led_status(&self, value: u8) -> nonblock::MethodReply<()> {
        <Self as nonblock::stdintf::org_freedesktop_dbus::Properties>::set(self, "fr.enedis.HAL.LEDManager1", "PowerLedStatus", value)
    }

    fn set_eth1_led_status(&self, value: u8) -> nonblock::MethodReply<()> {
        <Self as nonblock::stdintf::org_freedesktop_dbus::Properties>::set(self, "fr.enedis.HAL.LEDManager1", "Eth1LedStatus", value)
    }

    fn set_eth2_led_status(&self, value: u8) -> nonblock::MethodReply<()> {
        <Self as nonblock::stdintf::org_freedesktop_dbus::Properties>::set(self, "fr.enedis.HAL.LEDManager1", "Eth2LedStatus", value)
    }

    fn set_cled_status(&self, value: u8) -> nonblock::MethodReply<()> {
        <Self as nonblock::stdintf::org_freedesktop_dbus::Properties>::set(self, "fr.enedis.HAL.LEDManager1", "CLedStatus", value)
    }

    fn set_rslled_status_and_color(&self, value: u8) -> nonblock::MethodReply<()> {
        <Self as nonblock::stdintf::org_freedesktop_dbus::Properties>::set(self, "fr.enedis.HAL.LEDManager1", "RSLLedStatusAndColor", value)
    }
}
