// This code was autogenerated with `dbus-codegen-rust --file /tmp/dbus-api/src/main/dbus/specs/fr.enedis.HAL.PartitionManager1.xml --output crates/dbus-api/src/partition_manager.rs --client nonblock`, see https://github.com/diwic/dbus-rs
use dbus as dbus;
#[allow(unused_imports)]
use dbus::arg;
use dbus::nonblock;

pub trait FrEnedisHALPartitionManager1 {
    fn mount_read_only(&self, partition: u8) -> nonblock::MethodReply<String>;
    fn mount_read_write(&self, partition: u8) -> nonblock::MethodReply<String>;
    fn umount(&self, partition_name: u8, force: bool) -> nonblock::MethodReply<()>;
    fn copy_partition(&self, destination_partition_name: u8, source_partition_name: u8) -> nonblock::MethodReply<()>;
    fn copy_data_to_partition(&self, destination_partition_name: u8, data: &str) -> nonblock::MethodReply<()>;
    fn get_number_error_blocs(&self, partition_name: u8) -> nonblock::MethodReply<u32>;
    fn wipe_client_data_partition(&self) -> nonblock::MethodReply<()>;
}

impl<'a, T: nonblock::NonblockReply, C: ::std::ops::Deref<Target=T>> FrEnedisHALPartitionManager1 for nonblock::Proxy<'a, C> {

    fn mount_read_only(&self, partition: u8) -> nonblock::MethodReply<String> {
        self.method_call("fr.enedis.HAL.PartitionManager1", "MountReadOnly", (partition, ))
            .and_then(|r: (String, )| Ok(r.0, ))
    }

    fn mount_read_write(&self, partition: u8) -> nonblock::MethodReply<String> {
        self.method_call("fr.enedis.HAL.PartitionManager1", "MountReadWrite", (partition, ))
            .and_then(|r: (String, )| Ok(r.0, ))
    }

    fn umount(&self, partition_name: u8, force: bool) -> nonblock::MethodReply<()> {
        self.method_call("fr.enedis.HAL.PartitionManager1", "UMount", (partition_name, force, ))
    }

    fn copy_partition(&self, destination_partition_name: u8, source_partition_name: u8) -> nonblock::MethodReply<()> {
        self.method_call("fr.enedis.HAL.PartitionManager1", "CopyPartition", (destination_partition_name, source_partition_name, ))
    }

    fn copy_data_to_partition(&self, destination_partition_name: u8, data: &str) -> nonblock::MethodReply<()> {
        self.method_call("fr.enedis.HAL.PartitionManager1", "CopyDataToPartition", (destination_partition_name, data, ))
    }

    fn get_number_error_blocs(&self, partition_name: u8) -> nonblock::MethodReply<u32> {
        self.method_call("fr.enedis.HAL.PartitionManager1", "GetNumberErrorBlocs", (partition_name, ))
            .and_then(|r: (u32, )| Ok(r.0, ))
    }

    fn wipe_client_data_partition(&self) -> nonblock::MethodReply<()> {
        self.method_call("fr.enedis.HAL.PartitionManager1", "WipeClientDataPartition", ())
    }
}
