// This code was autogenerated with `dbus-codegen-rust --file /tmp/dbus-api/src/main/dbus/specs/fr.enedis.HAL.ServicesDBUS1.xml --output crates/dbus-api/src/services_dbus.rs --client nonblock`, see https://github.com/diwic/dbus-rs
use dbus as dbus;
#[allow(unused_imports)]
use dbus::arg;
use dbus::nonblock;

pub trait FrEnedisHALServiceDBUS1 {
    fn version(&self) -> nonblock::MethodReply<String>;
    fn log_level(&self) -> nonblock::MethodReply<String>;
    fn setlog_level(&self, value: String) -> nonblock::MethodReply<()>;
}

impl<'a, T: nonblock::NonblockReply, C: ::std::ops::Deref<Target=T>> FrEnedisHALServiceDBUS1 for nonblock::Proxy<'a, C> {

    fn version(&self) -> nonblock::MethodReply<String> {
        <Self as nonblock::stdintf::org_freedesktop_dbus::Properties>::get(self, "fr.enedis.HAL.ServiceDBUS1", "version")
    }

    fn log_level(&self) -> nonblock::MethodReply<String> {
        <Self as nonblock::stdintf::org_freedesktop_dbus::Properties>::get(self, "fr.enedis.HAL.ServiceDBUS1", "logLevel")
    }

    fn setlog_level(&self, value: String) -> nonblock::MethodReply<()> {
        <Self as nonblock::stdintf::org_freedesktop_dbus::Properties>::set(self, "fr.enedis.HAL.ServiceDBUS1", "logLevel", value)
    }
}
