[package]
name = "i2r-network"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
clap = { version = "4.5.31", features = ["derive"] }
## Gestion d'erreurs
# Anyhow permet un certain sucre syntaxique autour des erreurs en Rust
anyhow = { version = "1.0.93" }

## Asynchrone
# Tokio est le moteur de parallélisation de référence en Rust
tokio = { version = "1", features = ["macros", "rt-multi-thread"] }

## Logs
# Tracing est la référence afin de tracer les appels du boitier
tracing = { version = "0.1.40" }
tracing-subscriber = { version = "0.3.18", features = ["env-filter"] }
tracing-appender = "0.2.3"

network = { path = "../../crates/network" }
infra = { path = "../../crates/infra" }
