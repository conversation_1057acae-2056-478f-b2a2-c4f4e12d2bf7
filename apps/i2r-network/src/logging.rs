use anyhow::Context;
use network::errors::NetworkError;
use tracing_appender::{
    non_blocking::WorkerGuard,
    rolling::{RollingFileAppender, Rotation},
};
use tracing_subscriber::{fmt::Layer, layer::SubscriberExt, util::SubscriberInitExt, EnvFilter};

pub fn setup() -> Result<WorkerGuard, NetworkError> {
    let file_appender = RollingFileAppender::builder()
        .rotation(Rotation::NEVER)
        .filename_prefix("i2r-network")
        .filename_suffix("log")
        .build("/var/log/i2r")?;

    let (non_blocking, _guard) = tracing_appender::non_blocking(file_appender);

    let base_layer = Layer::default()
        // enable everything
        .compact()
        // Display source code file paths
        .with_file(false)
        // Display source code line numbers
        .with_line_number(false)
        // Don't display the event's target (module path)
        .with_target(false);

    let file_layer = Layer::default()
        // enable everything
        .compact()
        // Display source code file paths
        .with_file(false)
        // Display source code line numbers
        .with_line_number(false)
        // Display the thread ID an event was recorded on
        .with_thread_ids(false)
        // Don't display the event's target (module path)
        .with_target(false)
        .with_writer(non_blocking);

    tracing_subscriber::registry()
        .with(EnvFilter::from_default_env().add_directive(tracing::Level::DEBUG.into()))
        .with(base_layer)
        .with(file_layer)
        .try_init()
        .context("setup logger global")?;

    Ok(_guard)
}
