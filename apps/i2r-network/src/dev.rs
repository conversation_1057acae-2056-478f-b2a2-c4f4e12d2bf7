#[derive(Clone)]
pub struct Identite {
    pub ads: &'static str,
    pub radius: &'static str,
    pub idms: &'static str,
}

pub fn identite_from_ads(ads: &str) -> Option<Identite> {
    IDENTITES
        .into_iter()
        .cloned()
        .find(|identite| identite.ads == ads)
}

pub const IDENTITES: &'static [Identite] = &[
    Identite {
        ads: "0225DB00000225",
        radius: "oycpFS0R+h0+e/MDKbVkMA==",
        idms: "475000DE007860000010000001000354",
    },
    Identite {
        ads: "0625DB00000500",
        radius: "tycpGS0R+e0+e/jhKbVkMR==",
        idms: "475000DE007860000010000001000111",
    },
    Identite {
        ads: "0625DB00000600",
        radius: "tycpDRg0R+e0+e/jhKbVkMR==",
        idms: "475000DE007860000010000001000222",
    },
    Identite {
        ads: "6625DB00000650",
        radius: "testCCg0R+e0+e/jhKbSkTR-o",
        idms: "475000DE007860000010000001000333",
    },
    Identite {
        ads: "ADSDETEST",
        radius: "RADIUSDETEST",
        idms: "IDMSDETEST",
    },
];
