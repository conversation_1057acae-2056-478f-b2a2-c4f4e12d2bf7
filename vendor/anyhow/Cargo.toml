# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2018"
rust-version = "1.39"
name = "anyhow"
version = "1.0.97"
authors = ["<PERSON> <<EMAIL>>"]
build = "build.rs"
autolib = false
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = "Flexible concrete Error type built on std::error::Error"
documentation = "https://docs.rs/anyhow"
readme = "README.md"
keywords = [
    "error",
    "error-handling",
]
categories = [
    "rust-patterns",
    "no-std",
]
license = "MIT OR Apache-2.0"
repository = "https://github.com/dtolnay/anyhow"

[package.metadata.docs.rs]
rustdoc-args = [
    "--generate-link-to-definition",
    "--extern-html-root-url=core=https://doc.rust-lang.org",
    "--extern-html-root-url=alloc=https://doc.rust-lang.org",
    "--extern-html-root-url=std=https://doc.rust-lang.org",
]
targets = ["x86_64-unknown-linux-gnu"]

[features]
default = ["std"]
std = []

[lib]
name = "anyhow"
path = "src/lib.rs"

[[test]]
name = "compiletest"
path = "tests/compiletest.rs"

[[test]]
name = "test_autotrait"
path = "tests/test_autotrait.rs"

[[test]]
name = "test_backtrace"
path = "tests/test_backtrace.rs"

[[test]]
name = "test_boxed"
path = "tests/test_boxed.rs"

[[test]]
name = "test_chain"
path = "tests/test_chain.rs"

[[test]]
name = "test_context"
path = "tests/test_context.rs"

[[test]]
name = "test_convert"
path = "tests/test_convert.rs"

[[test]]
name = "test_downcast"
path = "tests/test_downcast.rs"

[[test]]
name = "test_ensure"
path = "tests/test_ensure.rs"

[[test]]
name = "test_ffi"
path = "tests/test_ffi.rs"

[[test]]
name = "test_fmt"
path = "tests/test_fmt.rs"

[[test]]
name = "test_macros"
path = "tests/test_macros.rs"

[[test]]
name = "test_repr"
path = "tests/test_repr.rs"

[[test]]
name = "test_source"
path = "tests/test_source.rs"

[dependencies.backtrace]
version = "0.3.51"
optional = true

[dev-dependencies.futures]
version = "0.3"
default-features = false

[dev-dependencies.rustversion]
version = "1.0.6"

[dev-dependencies.syn]
version = "2.0"
features = ["full"]

[dev-dependencies.thiserror]
version = "2"

[dev-dependencies.trybuild]
version = "1.0.66"
features = ["diff"]
