error: unexpected end of macro invocation
 --> tests/ui/empty-ensure.rs:4:5
  |
4 |     ensure!();
  |     ^^^^^^^^^ missing tokens in macro arguments
  |
note: while trying to match meta-variable `$cond:expr`
 --> src/ensure.rs
  |
  |     ($cond:expr $(,)?) => {
  |      ^^^^^^^^^^
  = note: this error originates in the macro `$crate::__parse_ensure` which comes from the expansion of the macro `ensure` (in Nightly builds, run with -Z macro-backtrace for more info)
