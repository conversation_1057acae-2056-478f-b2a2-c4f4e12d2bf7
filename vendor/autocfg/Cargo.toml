# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
rust-version = "1.0"
name = "autocfg"
version = "1.4.0"
authors = ["<PERSON> <<EMAIL>>"]
build = false
exclude = ["/.github/**"]
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = "Automatic cfg for Rust compiler features"
documentation = "https://docs.rs/autocfg/"
readme = "README.md"
keywords = [
    "rustc",
    "build",
    "autoconf",
]
categories = ["development-tools::build-utils"]
license = "Apache-2.0 OR MIT"
repository = "https://github.com/cuviper/autocfg"

[lib]
name = "autocfg"
path = "src/lib.rs"

[[example]]
name = "integers"
path = "examples/integers.rs"

[[example]]
name = "nightly"
path = "examples/nightly.rs"

[[example]]
name = "paths"
path = "examples/paths.rs"

[[example]]
name = "traits"
path = "examples/traits.rs"

[[example]]
name = "versions"
path = "examples/versions.rs"

[[test]]
name = "no_std"
path = "tests/no_std.rs"

[[test]]
name = "rustflags"
path = "tests/rustflags.rs"

[[test]]
name = "tests"
path = "tests/tests.rs"

[[test]]
name = "wrappers"
path = "tests/wrappers.rs"

[dependencies]
