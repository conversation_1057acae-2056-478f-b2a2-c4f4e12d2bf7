# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies
#
# If you believe there's an error in this file please file an
# issue against the rust-lang/cargo repository. If you're
# editing this file be aware that the upstream Cargo.toml
# will likely look very different (and much more reasonable)

[package]
edition = "2018"
name = "cfg-if"
version = "1.0.0"
authors = ["<PERSON> <<EMAIL>>"]
description = "A macro to ergonomically define an item depending on a large number of #[cfg]\nparameters. Structured like an if-else chain, the first matching branch is the\nitem that gets emitted.\n"
homepage = "https://github.com/alexcrichton/cfg-if"
documentation = "https://docs.rs/cfg-if"
readme = "README.md"
license = "MIT/Apache-2.0"
repository = "https://github.com/alexcrichton/cfg-if"
[dependencies.compiler_builtins]
version = "0.1.2"
optional = true

[dependencies.core]
version = "1.0.0"
optional = true
package = "rustc-std-workspace-core"

[features]
rustc-dep-of-std = ["core", "compiler_builtins"]
[badges.travis-ci]
repository = "alexcrichton/cfg-if"
