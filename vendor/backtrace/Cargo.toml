# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2021"
rust-version = "1.65.0"
name = "backtrace"
version = "0.3.74"
authors = ["The Rust Project Developers"]
build = false
exclude = ["/ci/"]
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = """
A library to acquire a stack trace (backtrace) at runtime in a Rust program.
"""
homepage = "https://github.com/rust-lang/backtrace-rs"
documentation = "https://docs.rs/backtrace"
readme = "README.md"
license = "MIT OR Apache-2.0"
repository = "https://github.com/rust-lang/backtrace-rs"

[lib]
name = "backtrace"
path = "src/lib.rs"

[[example]]
name = "backtrace"
path = "examples/backtrace.rs"
required-features = ["std"]

[[example]]
name = "raw"
path = "examples/raw.rs"
required-features = ["std"]

[[test]]
name = "accuracy"
path = "tests/accuracy/main.rs"
required-features = ["std"]
edition = "2021"

[[test]]
name = "concurrent-panics"
path = "tests/concurrent-panics.rs"
harness = false
required-features = ["std"]

[[test]]
name = "current-exe-mismatch"
path = "tests/current-exe-mismatch.rs"
harness = false
required-features = ["std"]

[[test]]
name = "long_fn_name"
path = "tests/long_fn_name.rs"
required-features = ["std"]

[[test]]
name = "sgx-image-base"
path = "tests/sgx-image-base.rs"

[[test]]
name = "skip_inner_frames"
path = "tests/skip_inner_frames.rs"
required-features = ["std"]

[[test]]
name = "smoke"
path = "tests/smoke.rs"
required-features = ["std"]
edition = "2021"

[[bench]]
name = "benchmarks"
path = "benches/benchmarks.rs"

[dependencies.cfg-if]
version = "1.0"

[dependencies.cpp_demangle]
version = "0.4.0"
features = ["alloc"]
optional = true
default-features = false

[dependencies.rustc-demangle]
version = "0.1.24"

[dependencies.serde]
version = "1.0"
features = ["derive"]
optional = true

[dev-dependencies.libloading]
version = "0.7"

[features]
coresymbolication = []
dbghelp = []
default = ["std"]
dl_iterate_phdr = []
dladdr = []
kernel32 = []
libunwind = []
serialize-serde = ["serde"]
std = []
unix-backtrace = []

[target.'cfg(not(all(windows, target_env = "msvc", not(target_vendor = "uwp"))))'.dependencies.addr2line]
version = "0.24.0"
default-features = false

[target.'cfg(not(all(windows, target_env = "msvc", not(target_vendor = "uwp"))))'.dependencies.libc]
version = "0.2.156"
default-features = false

[target.'cfg(not(all(windows, target_env = "msvc", not(target_vendor = "uwp"))))'.dependencies.miniz_oxide]
version = "0.8"
default-features = false

[target.'cfg(not(all(windows, target_env = "msvc", not(target_vendor = "uwp"))))'.dependencies.object]
version = "0.36.0"
features = [
    "read_core",
    "elf",
    "macho",
    "pe",
    "xcoff",
    "unaligned",
    "archive",
]
default-features = false

[target."cfg(windows)".dependencies.windows-targets]
version = "0.52.6"

[lints.rust]
unexpected_cfgs = "allow"
