--out src/windows_sys.rs
--config sys flatten
--filter
Windows.Win32.Foundation.CloseHandle
Windows.Win32.Foundation.FALSE
Windows.Win32.Foundation.HINSTANCE
Windows.Win32.Foundation.INVALID_HANDLE_VALUE
Windows.Win32.Foundation.TRUE
Windows.Win32.Globalization.CP_UTF8
Windows.Win32.Globalization.lstrlenW
Windows.Win32.Globalization.WideCharToMultiByte
Windows.Win32.System.Diagnostics.Debug.AddrModeFlat
Windows.Win32.System.Diagnostics.Debug.CONTEXT
Windows.Win32.System.Diagnostics.Debug.EnumerateLoadedModulesW64
Windows.Win32.System.Diagnostics.Debug.IMAGEHLP_LINEW64
Windows.Win32.System.Diagnostics.Debug.MAX_SYM_NAME
Windows.Win32.System.Diagnostics.Debug.PENUMLOADED_MODULES_CALLBACKW64
Windows.Win32.System.Diagnostics.Debug.PFUNCTION_TABLE_ACCESS_ROUTINE64
Windows.Win32.System.Diagnostics.Debug.PGET_MODULE_BASE_ROUTINE64
Windows.Win32.System.Diagnostics.Debug.PREAD_PROCESS_MEMORY_ROUTINE64
Windows.Win32.System.Diagnostics.Debug.PTRANSLATE_ADDRESS_ROUTINE64
Windows.Win32.System.Diagnostics.Debug.RtlCaptureContext
Windows.Win32.System.Diagnostics.Debug.RtlLookupFunctionEntry
Windows.Win32.System.Diagnostics.Debug.RtlVirtualUnwind
Windows.Win32.System.Diagnostics.Debug.STACKFRAME64
Windows.Win32.System.Diagnostics.Debug.STACKFRAME_EX
Windows.Win32.System.Diagnostics.Debug.StackWalk64
Windows.Win32.System.Diagnostics.Debug.StackWalkEx
Windows.Win32.System.Diagnostics.Debug.SymAddrIncludeInlineTrace
Windows.Win32.System.Diagnostics.Debug.SYMBOL_INFOW
Windows.Win32.System.Diagnostics.Debug.SymFromAddrW
Windows.Win32.System.Diagnostics.Debug.SymFromInlineContextW
Windows.Win32.System.Diagnostics.Debug.SymFunctionTableAccess64
Windows.Win32.System.Diagnostics.Debug.SymGetLineFromAddrW64
Windows.Win32.System.Diagnostics.Debug.SymGetLineFromInlineContextW
Windows.Win32.System.Diagnostics.Debug.SymGetModuleBase64
Windows.Win32.System.Diagnostics.Debug.SymGetOptions
Windows.Win32.System.Diagnostics.Debug.SymGetSearchPathW
Windows.Win32.System.Diagnostics.Debug.SymInitializeW
Windows.Win32.System.Diagnostics.Debug.SYMOPT_DEFERRED_LOADS
Windows.Win32.System.Diagnostics.Debug.SymQueryInlineTrace
Windows.Win32.System.Diagnostics.Debug.SymSetOptions
Windows.Win32.System.Diagnostics.Debug.SymSetSearchPathW
Windows.Win32.System.Diagnostics.ToolHelp.CreateToolhelp32Snapshot
Windows.Win32.System.Diagnostics.ToolHelp.Module32FirstW
Windows.Win32.System.Diagnostics.ToolHelp.Module32NextW
Windows.Win32.System.Diagnostics.ToolHelp.MODULEENTRY32W
Windows.Win32.System.Diagnostics.ToolHelp.TH32CS_SNAPMODULE
Windows.Win32.System.LibraryLoader.GetProcAddress
Windows.Win32.System.LibraryLoader.LoadLibraryA
Windows.Win32.System.Memory.CreateFileMappingA
Windows.Win32.System.Memory.FILE_MAP_READ
Windows.Win32.System.Memory.MapViewOfFile
Windows.Win32.System.Memory.PAGE_READONLY
Windows.Win32.System.Memory.UnmapViewOfFile
Windows.Win32.System.SystemInformation.IMAGE_FILE_MACHINE_I386
Windows.Win32.System.Threading.CreateMutexA
Windows.Win32.System.Threading.GetCurrentProcess
Windows.Win32.System.Threading.GetCurrentProcessId
Windows.Win32.System.Threading.GetCurrentThread
Windows.Win32.System.Threading.INFINITE
Windows.Win32.System.Threading.ReleaseMutex
Windows.Win32.System.Threading.WaitForSingleObjectEx