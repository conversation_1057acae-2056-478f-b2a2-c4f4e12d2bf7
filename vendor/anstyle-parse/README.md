# anstyle-parse

> Parse [Parse ANSI Style Escapes](https://vt100.net/emu/dec_ansi_parser)

[![Documentation](https://img.shields.io/badge/docs-master-blue.svg)][Documentation]
![License](https://img.shields.io/crates/l/anstyle-parse.svg)
[![Crates Status](https://img.shields.io/crates/v/anstyle-parse.svg)](https://crates.io/crates/anstyle-parse)

## License

Licensed under either of

 * Apache License, Version 2.0, ([LICENSE-APACHE](LICENSE-APACHE) or http://www.apache.org/licenses/LICENSE-2.0)
 * MIT license ([LICENSE-MIT](LICENSE-MIT) or http://opensource.org/licenses/MIT)

at your option.

### Contribution

Unless you explicitly state otherwise, any contribution intentionally
submitted for inclusion in the work by you, as defined in the Apache-2.0
license, shall be dual licensed as above, without any additional terms or
conditions.

### Special Thanks

[chrisduerr](https://github.com/alacritty/vte/commits?author=chrisduerr) and the
[alacritty project](https://github.com/alacritty/alacritty) for
[vte](https://crates.io/crates/vte) which
[this was forked from](https://github.com/alacritty/vte/issues/82)

[Crates.io]: https://crates.io/crates/anstyle-parse
[Documentation]: https://docs.rs/anstyle-parse
