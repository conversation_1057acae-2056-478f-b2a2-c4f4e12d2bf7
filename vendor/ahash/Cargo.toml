# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2018"
rust-version = "1.60.0"
name = "ahash"
version = "0.8.11"
authors = ["<PERSON> <<EMAIL>>"]
build = "./build.rs"
exclude = [
    "/smhasher",
    "/benchmark_tools",
]
description = "A non-cryptographic hash function using AES-NI for high performance"
documentation = "https://docs.rs/ahash"
readme = "README.md"
keywords = [
    "hash",
    "hasher",
    "hashmap",
    "aes",
    "no-std",
]
categories = [
    "algorithms",
    "data-structures",
    "no-std",
]
license = "MIT OR Apache-2.0"
repository = "https://github.com/tkaitchuck/ahash"

[package.metadata.docs.rs]
features = ["std"]
rustc-args = [
    "-C",
    "target-feature=+aes",
]
rustdoc-args = [
    "-C",
    "target-feature=+aes",
]

[profile.bench]
opt-level = 3
lto = "fat"
codegen-units = 1
debug = 0
debug-assertions = false

[profile.release]
opt-level = 3
lto = "fat"
codegen-units = 1
debug = 0
debug-assertions = false

[profile.test]
opt-level = 2
lto = "fat"

[lib]
name = "ahash"
path = "src/lib.rs"
test = true
doctest = true
bench = true
doc = true

[[bench]]
name = "ahash"
path = "tests/bench.rs"
harness = false

[[bench]]
name = "map"
path = "tests/map_tests.rs"
harness = false

[dependencies.atomic-polyfill]
version = "1.0.1"
optional = true

[dependencies.cfg-if]
version = "1.0"

[dependencies.const-random]
version = "0.1.17"
optional = true

[dependencies.getrandom]
version = "0.2.7"
optional = true

[dependencies.serde]
version = "1.0.117"
optional = true

[dependencies.zerocopy]
version = "0.7.31"
features = ["simd"]
default-features = false

[dev-dependencies.criterion]
version = "0.3.2"
features = ["html_reports"]

[dev-dependencies.fnv]
version = "1.0.5"

[dev-dependencies.fxhash]
version = "0.2.1"

[dev-dependencies.hashbrown]
version = "0.14.3"

[dev-dependencies.hex]
version = "0.4.2"

[dev-dependencies.no-panic]
version = "0.1.10"

[dev-dependencies.pcg-mwc]
version = "0.2.1"

[dev-dependencies.rand]
version = "0.8.5"

[dev-dependencies.seahash]
version = "4.0"

[dev-dependencies.serde_json]
version = "1.0.59"

[dev-dependencies.smallvec]
version = "1.13.1"

[build-dependencies.version_check]
version = "0.9.4"

[features]
atomic-polyfill = [
    "dep:atomic-polyfill",
    "once_cell/atomic-polyfill",
]
compile-time-rng = ["const-random"]
default = [
    "std",
    "runtime-rng",
]
nightly-arm-aes = []
no-rng = []
runtime-rng = ["getrandom"]
std = []

[target."cfg(not(all(target_arch = \"arm\", target_os = \"none\")))".dependencies.once_cell]
version = "1.18.0"
features = ["alloc"]
default-features = false
