# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2021"
rust-version = "1.60.0"
name = "aho-corasick"
version = "1.1.3"
authors = ["<PERSON> <<EMAIL>>"]
exclude = [
    "/aho-corasick-debug",
    "/benchmarks",
    "/tmp",
]
autotests = false
description = "Fast multiple substring searching."
homepage = "https://github.com/BurntSushi/aho-corasick"
readme = "README.md"
keywords = [
    "string",
    "search",
    "text",
    "pattern",
    "multi",
]
categories = ["text-processing"]
license = "Unlicense OR MIT"
repository = "https://github.com/BurntSushi/aho-corasick"

[package.metadata.docs.rs]
all-features = true
rustdoc-args = [
    "--cfg",
    "docsrs",
    "--generate-link-to-definition",
]

[profile.bench]
debug = 2

[profile.release]
debug = 2

[lib]
name = "aho_corasick"

[dependencies.log]
version = "0.4.17"
optional = true

[dependencies.memchr]
version = "2.4.0"
optional = true
default-features = false

[dev-dependencies.doc-comment]
version = "0.3.3"

[features]
default = [
    "std",
    "perf-literal",
]
logging = ["dep:log"]
perf-literal = ["dep:memchr"]
std = ["memchr?/std"]
