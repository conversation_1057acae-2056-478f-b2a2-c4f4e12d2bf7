# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2018"
rust-version = "1.65"
name = "addr2line"
version = "0.24.2"
build = false
include = [
    "/CHANGELOG.md",
    "/Cargo.lock",
    "/Cargo.toml",
    "/LICENSE-APACHE",
    "/LICENSE-MIT",
    "/README.md",
    "/src",
]
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = "A cross-platform symbolication library written in Rust, using `gimli`"
documentation = "https://docs.rs/addr2line"
readme = "README.md"
keywords = [
    "DWARF",
    "debug",
    "elf",
    "symbolicate",
    "atos",
]
categories = ["development-tools::debugging"]
license = "Apache-2.0 OR MIT"
repository = "https://github.com/gimli-rs/addr2line"

[profile.bench]
codegen-units = 1
debug = 2

[profile.release]
debug = 2

[lib]
name = "addr2line"
path = "src/lib.rs"

[[bin]]
name = "addr2line"
path = "src/bin/addr2line.rs"
required-features = ["bin"]

[dependencies.alloc]
version = "1.0.0"
optional = true
package = "rustc-std-workspace-alloc"

[dependencies.clap]
version = "4.3.21"
features = ["wrap_help"]
optional = true

[dependencies.compiler_builtins]
version = "0.1.2"
optional = true

[dependencies.core]
version = "1.0.0"
optional = true
package = "rustc-std-workspace-core"

[dependencies.cpp_demangle]
version = "0.4"
features = ["alloc"]
optional = true
default-features = false

[dependencies.fallible-iterator]
version = "0.3.0"
optional = true
default-features = false

[dependencies.gimli]
version = "0.31.1"
features = ["read"]
default-features = false

[dependencies.memmap2]
version = "0.9.4"
optional = true

[dependencies.object]
version = "0.36.0"
features = [
    "read",
    "compression",
]
optional = true
default-features = false

[dependencies.rustc-demangle]
version = "0.1"
optional = true

[dependencies.smallvec]
version = "1"
optional = true
default-features = false

[dependencies.typed-arena]
version = "2"
optional = true

[dev-dependencies.backtrace]
version = "0.3.13"

[dev-dependencies.findshlibs]
version = "0.10"

[dev-dependencies.libtest-mimic]
version = "0.7.2"

[features]
all = ["bin"]
bin = [
    "loader",
    "rustc-demangle",
    "cpp_demangle",
    "fallible-iterator",
    "smallvec",
    "dep:clap",
]
cargo-all = []
default = [
    "rustc-demangle",
    "cpp_demangle",
    "loader",
    "fallible-iterator",
    "smallvec",
]
loader = [
    "std",
    "dep:object",
    "dep:memmap2",
    "dep:typed-arena",
]
rustc-dep-of-std = [
    "core",
    "alloc",
    "compiler_builtins",
    "gimli/rustc-dep-of-std",
]
std = ["gimli/std"]
