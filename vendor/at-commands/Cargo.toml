# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2021"
name = "at-commands"
version = "0.5.5"
authors = ["Dion Dokter <<EMAIL>>"]
build = false
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = "AT Commands builder and parser for Rust #![no_std]"
homepage = "https://github.com/diondokter/at-commands"
readme = "README.md"
keywords = [
    "no_std",
    "AT",
    "Command",
    "Builder",
]
license = "MIT OR Apache-2.0"
repository = "https://github.com/diondokter/at-commands"

[lib]
name = "at_commands"
path = "src/lib.rs"

[dependencies.defmt]
version = "0.3"
optional = true

[features]
defmt = ["dep:defmt"]
