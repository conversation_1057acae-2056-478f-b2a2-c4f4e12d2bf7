# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2021"
rust-version = "1.66.0"
name = "anstream"
version = "0.6.18"
build = false
include = [
    "build.rs",
    "src/**/*",
    "Cargo.toml",
    "Cargo.lock",
    "LICENSE*",
    "README.md",
    "benches/**/*",
    "examples/**/*",
]
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = "A simple cross platform library for writing colored text to a terminal."
homepage = "https://github.com/rust-cli/anstyle"
readme = "README.md"
keywords = [
    "ansi",
    "terminal",
    "color",
    "strip",
    "wincon",
]
categories = ["command-line-interface"]
license = "MIT OR Apache-2.0"
repository = "https://github.com/rust-cli/anstyle.git"

[package.metadata.docs.rs]
all-features = true
rustdoc-args = [
    "--cfg",
    "docsrs",
]

[[package.metadata.release.pre-release-replacements]]
file = "CHANGELOG.md"
min = 1
replace = "{{version}}"
search = "Unreleased"

[[package.metadata.release.pre-release-replacements]]
exactly = 1
file = "CHANGELOG.md"
replace = "...{{tag_name}}"
search = '\.\.\.HEAD'

[[package.metadata.release.pre-release-replacements]]
file = "CHANGELOG.md"
min = 1
replace = "{{date}}"
search = "ReleaseDate"

[[package.metadata.release.pre-release-replacements]]
exactly = 1
file = "CHANGELOG.md"
replace = """
<!-- next-header -->
## [Unreleased] - ReleaseDate
"""
search = "<!-- next-header -->"

[[package.metadata.release.pre-release-replacements]]
exactly = 1
file = "CHANGELOG.md"
replace = """
<!-- next-url -->
[Unreleased]: https://github.com/rust-cli/anstyle/compare/{{tag_name}}...HEAD"""
search = "<!-- next-url -->"

[lib]
name = "anstream"
path = "src/lib.rs"

[[example]]
name = "dump-stream"
path = "examples/dump-stream.rs"
required-features = ["auto"]

[[example]]
name = "query-stream"
path = "examples/query-stream.rs"
required-features = ["auto"]

[[bench]]
name = "stream"
path = "benches/stream.rs"
harness = false

[[bench]]
name = "strip"
path = "benches/strip.rs"
harness = false

[[bench]]
name = "wincon"
path = "benches/wincon.rs"
harness = false

[dependencies.anstyle]
version = "1.0.0"

[dependencies.anstyle-parse]
version = "0.2.0"

[dependencies.anstyle-query]
version = "1.0.0"
optional = true

[dependencies.colorchoice]
version = "1.0.0"

[dependencies.is_terminal_polyfill]
version = "1.48"

[dependencies.utf8parse]
version = "0.2.1"

[dev-dependencies.divan]
version = "0.1.11"

[dev-dependencies.lexopt]
version = "0.3.0"

[dev-dependencies.owo-colors]
version = "4.0.0"

[dev-dependencies.proptest]
version = "1.4.0"

[dev-dependencies.strip-ansi-escapes]
version = "0.2.0"

[features]
auto = ["dep:anstyle-query"]
default = [
    "auto",
    "wincon",
]
test = []
wincon = ["dep:anstyle-wincon"]

[target."cfg(windows)".dependencies.anstyle-wincon]
version = "3.0.5"
optional = true

[lints.clippy]
bool_assert_comparison = "allow"
branches_sharing_code = "allow"
checked_conversions = "warn"
collapsible_else_if = "allow"
create_dir = "warn"
dbg_macro = "warn"
debug_assert_with_mut_call = "warn"
doc_markdown = "warn"
empty_enum = "warn"
enum_glob_use = "warn"
expl_impl_clone_on_copy = "warn"
explicit_deref_methods = "warn"
explicit_into_iter_loop = "warn"
fallible_impl_from = "warn"
filter_map_next = "warn"
flat_map_option = "warn"
float_cmp_const = "warn"
fn_params_excessive_bools = "warn"
from_iter_instead_of_collect = "warn"
if_same_then_else = "allow"
implicit_clone = "warn"
imprecise_flops = "warn"
inconsistent_struct_constructor = "warn"
inefficient_to_string = "warn"
infinite_loop = "warn"
invalid_upcast_comparisons = "warn"
large_digit_groups = "warn"
large_stack_arrays = "warn"
large_types_passed_by_value = "warn"
let_and_return = "allow"
linkedlist = "warn"
lossy_float_literal = "warn"
macro_use_imports = "warn"
mem_forget = "warn"
mutex_integer = "warn"
needless_continue = "warn"
needless_for_each = "warn"
negative_feature_names = "warn"
path_buf_push_overwrite = "warn"
ptr_as_ptr = "warn"
rc_mutex = "warn"
redundant_feature_names = "warn"
ref_option_ref = "warn"
rest_pat_in_fully_bound_structs = "warn"
same_functions_in_if_condition = "warn"
self_named_module_files = "warn"
semicolon_if_nothing_returned = "warn"
str_to_string = "warn"
string_add = "warn"
string_add_assign = "warn"
string_lit_as_bytes = "warn"
string_to_string = "warn"
todo = "warn"
trait_duplication_in_bounds = "warn"
uninlined_format_args = "warn"
verbose_file_reads = "warn"
wildcard_imports = "warn"
zero_sized_map_values = "warn"

[lints.rust]
unreachable_pub = "warn"
unsafe_op_in_unsafe_fn = "warn"
unused_lifetimes = "warn"
unused_macro_rules = "warn"
unused_qualifications = "warn"

[lints.rust.rust_2018_idioms]
level = "warn"
priority = -1
