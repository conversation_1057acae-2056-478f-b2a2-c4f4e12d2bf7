{
    "files.exclude": {
        "**/.git": true,
        "**/.svn": true,
        "**/.hg": true,
        "**/.DS_Store": true,
        "**/Thumbs.db": true,
        "**/vendor": true
    },
    "editor.formatOnSave": true,
    "files.readonlyInclude": {
        "**/.cargo/registry/src/**/*.rs": true,
        "**/lib/rustlib/src/rust/library/**/*.rs": true,
    },
    "[rust]": {
        "editor.defaultFormatter": "rust-lang.rust-analyzer",
    },
}