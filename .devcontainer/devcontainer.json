{"build": {"dockerfile": "../Dockerfile", "context": "..", "target": "dev", "options": ["--pull"]}, "onCreateCommand": {"startDbus": "sudo service dbus start"}, "mounts": ["type=bind,source=/home/<USER>/.ssh,target=/home/<USER>/.ssh,readonly"], "remoteUser": "wudi", "customizations": {"vscode": {"extensions": ["ms-vsliveshare.vsliveshare", "eamodio.gitlens", "ms-azuretools.vscode-docker", "yzhang.markdown-all-in-one", "rust-lang.rust-analyzer", "tamasfe.even-better-toml"]}}}